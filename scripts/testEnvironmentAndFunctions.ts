/**
 * 環境配置和 Cloud Functions 綜合測試腳本
 * 測試環境是否正確跟隨 .env 中的 NODE_ENV 並測試不同的 Cloud Functions
 */

import { app, auth, firestoreDB, realtimeDB, functions } from '../firebaseConfig';
import { getEnvironmentInfo } from '../utils/firebaseCollections';
import { httpsCallable } from 'firebase/functions';

/**
 * 測試用戶數據生成
 */
function generateTestUserData() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  
  return {
    deviceID: `test_device_${timestamp}_${random}`,
    nickname: `Test User ${random}`,
    fcmToken: `test_fcm_token_${timestamp}_${random}`,
    name: 'Test User',
    role: 'Tester',
    initials: 'TU',
    color: '#3B82F6',
    phoneNumber: '******-0123',
    avatar: ''
  };
}

/**
 * 測試通知數據生成
 */
function generateTestAlertData(initiatorDeviceID: string, recipientDeviceIDs: string[]) {
  return {
    initiatorDeviceID,
    caseType: 'mother_baby_transfer',
    motherInitial: 'TM',
    bedNumber: 'B101',
    designatedWard: 'NICU',
    clinicalNotes: 'Test clinical notes for environment testing',
    recipientDeviceIDs
  };
}

/**
 * 測試環境配置
 */
async function testEnvironmentConfiguration(): Promise<void> {
  console.log('🔍 測試環境配置...');
  console.log('==========================================');
  
  // 1. 測試環境信息
  const envInfo = getEnvironmentInfo();
  console.log('📊 環境信息:');
  console.log(`   NODE_ENV: ${envInfo.nodeEnv}`);
  console.log(`   開發環境: ${envInfo.isDevelopment}`);
  console.log(`   Firebase 項目: ${envInfo.firebaseProject}`);
  console.log(`   隔離方法: ${envInfo.isolationMethod}`);
  console.log('');
  
  // 2. 測試 Firebase 配置
  console.log('🔧 Firebase 配置:');
  console.log(`   項目 ID: ${app.options.projectId}`);
  console.log(`   Auth Domain: ${app.options.authDomain}`);
  console.log(`   Database URL: ${app.options.databaseURL}`);
  console.log('');
  
  // 3. 測試集合配置
  console.log('📁 集合配置 (來自環境變數):');
  Object.entries(envInfo.collections.firestore).forEach(([key, value]) => {
    const envVar = `FIRESTORE_${key.toUpperCase()}_COLLECTION`;
    const envValue = process.env[envVar] || '未設置';
    console.log(`   ${key}: ${value} (${envVar}=${envValue})`);
  });
  console.log('');
  
  // 4. 驗證項目選擇
  const expectedProject = envInfo.isDevelopment ? 'qmnoti-test' : 'qmnoti';
  const actualProject = app.options.projectId;
  
  if (actualProject === expectedProject) {
    console.log(`✅ 項目選擇正確: ${actualProject}`);
  } else {
    console.log(`❌ 項目選擇錯誤: 期望 ${expectedProject}, 實際 ${actualProject}`);
    throw new Error('Firebase 項目配置錯誤');
  }
  
  console.log('');
}

/**
 * 測試 registerUser Cloud Function
 */
async function testRegisterUserFunction(): Promise<string> {
  console.log('👤 測試 registerUser Cloud Function...');
  
  try {
    const registerUser = httpsCallable(functions, 'registerUser');
    const testUser = generateTestUserData();
    
    console.log(`   測試用戶: ${testUser.nickname} (${testUser.deviceID})`);
    
    const result = await registerUser(testUser);
    const response = result.data as any;
    
    if (response.success) {
      console.log(`   ✅ 用戶註冊成功: ${response.message}`);
      return testUser.deviceID;
    } else {
      throw new Error(`用戶註冊失敗: ${response.message}`);
    }
  } catch (error) {
    console.log(`   ❌ 用戶註冊失敗:`, error);
    throw error;
  }
}

/**
 * 測試 createAlert Cloud Function
 */
async function testCreateAlertFunction(initiatorDeviceID: string, recipientDeviceID: string): Promise<string> {
  console.log('🚨 測試 createAlert Cloud Function...');
  
  try {
    const createAlert = httpsCallable(functions, 'createAlert');
    const alertData = generateTestAlertData(initiatorDeviceID, [recipientDeviceID]);
    
    console.log(`   發起者: ${initiatorDeviceID}`);
    console.log(`   接收者: ${recipientDeviceID}`);
    console.log(`   案例類型: ${alertData.caseType}`);
    
    const result = await createAlert(alertData);
    const response = result.data as any;
    
    if (response.success) {
      console.log(`   ✅ 通知創建成功: ${response.eventID}`);
      return response.eventID;
    } else {
      throw new Error(`通知創建失敗: ${response.message}`);
    }
  } catch (error) {
    console.log(`   ❌ 通知創建失敗:`, error);
    throw error;
  }
}

/**
 * 測試 acknowledgeAlert Cloud Function
 */
async function testAcknowledgeAlertFunction(eventID: string, recipientDeviceID: string): Promise<void> {
  console.log('✅ 測試 acknowledgeAlert Cloud Function...');
  
  try {
    const acknowledgeAlert = httpsCallable(functions, 'acknowledgeAlert');
    const acknowledgeData = {
      eventID,
      recipientDeviceID
    };
    
    console.log(`   事件 ID: ${eventID}`);
    console.log(`   確認者: ${recipientDeviceID}`);
    
    const result = await acknowledgeAlert(acknowledgeData);
    const response = result.data as any;
    
    if (response.success) {
      console.log(`   ✅ 通知確認成功: ${response.message}`);
    } else {
      throw new Error(`通知確認失敗: ${response.message}`);
    }
  } catch (error) {
    console.log(`   ❌ 通知確認失敗:`, error);
    throw error;
  }
}

/**
 * 測試 healthCheck Cloud Function
 */
async function testHealthCheckFunction(): Promise<void> {
  console.log('🏥 測試 healthCheck Cloud Function...');
  
  try {
    const healthCheck = httpsCallable(functions, 'healthCheck');
    
    const result = await healthCheck();
    const response = result.data as any;
    
    if (response.status === 'healthy') {
      console.log(`   ✅ 健康檢查通過: ${response.status}`);
      console.log(`   📊 環境: ${response.environment}`);
      console.log(`   🕐 時間戳: ${new Date(response.timestamp).toLocaleString()}`);
    } else {
      throw new Error(`健康檢查失敗: ${response.status}`);
    }
  } catch (error) {
    console.log(`   ❌ 健康檢查失敗:`, error);
    throw error;
  }
}

/**
 * 主測試函數
 */
async function runTests(): Promise<void> {
  console.log('🚀 開始環境配置和 Cloud Functions 測試...');
  console.log('==========================================');
  
  try {
    // 1. 測試環境配置
    await testEnvironmentConfiguration();
    
    // 2. 測試健康檢查
    await testHealthCheckFunction();
    
    // 3. 測試用戶註冊
    const initiatorDeviceID = await testRegisterUserFunction();
    
    // 等待一下確保數據寫入完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 4. 註冊第二個用戶作為接收者
    const recipientDeviceID = await testRegisterUserFunction();
    
    // 等待一下確保數據寫入完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 5. 測試創建通知
    const eventID = await testCreateAlertFunction(initiatorDeviceID, recipientDeviceID);
    
    // 等待一下確保數據寫入完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 6. 測試確認通知
    await testAcknowledgeAlertFunction(eventID, recipientDeviceID);
    
    console.log('');
    console.log('🎉 所有測試通過！');
    console.log('==========================================');
    console.log('✅ 環境配置正確');
    console.log('✅ Firebase 項目選擇正確');
    console.log('✅ 集合配置來自環境變數');
    console.log('✅ 所有 Cloud Functions 正常工作');
    
  } catch (error) {
    console.log('');
    console.log('❌ 測試失敗！');
    console.log('==========================================');
    console.error('錯誤詳情:', error);
    process.exit(1);
  }
}

// 執行測試
if (require.main === module) {
  runTests();
}

export { runTests };
