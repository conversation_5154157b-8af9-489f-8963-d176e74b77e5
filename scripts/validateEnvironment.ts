/**
 * 環境配置驗證腳本
 * 驗證 .env 配置和 Firebase 項目設置是否正確
 */

import { app } from '../firebaseConfig';
import { getEnvironmentInfo } from '../utils/firebaseCollections';

/**
 * 驗證環境變數配置
 */
function validateEnvironmentVariables(): { isValid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  console.log('🔍 驗證環境變數配置...');
  
  // 檢查 NODE_ENV
  const nodeEnv = process.env.NODE_ENV;
  if (!nodeEnv) {
    errors.push('NODE_ENV 未設置');
  } else if (nodeEnv !== 'development' && nodeEnv !== 'production') {
    warnings.push(`NODE_ENV 值不標準: ${nodeEnv} (建議使用 development 或 production)`);
  } else {
    console.log(`   ✅ NODE_ENV: ${nodeEnv}`);
  }
  
  // 檢查 Firestore 集合環境變數
  const firestoreVars = [
    'FIRESTORE_USERS_COLLECTION',
    'FIRESTORE_EVENTS_COLLECTION',
    'FIRESTORE_GROUPS_COLLECTION',
    'FIRESTORE_STAFF_COLLECTION'
  ];
  
  console.log('   📊 Firestore 集合配置:');
  firestoreVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
      warnings.push(`${varName} 未設置，將使用默認值`);
      console.log(`   ⚠️  ${varName}: 未設置 (使用默認值)`);
    } else {
      console.log(`   ✅ ${varName}: ${value}`);
    }
  });
  
  // 檢查 Realtime Database 路徑環境變數
  const realtimeVars = [
    'REALTIME_PRESENCE_PATH',
    'REALTIME_EVENTS_PATH',
    'REALTIME_STATS_PATH'
  ];
  
  console.log('   📈 Realtime Database 路徑配置:');
  realtimeVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
      warnings.push(`${varName} 未設置，將使用默認值`);
      console.log(`   ⚠️  ${varName}: 未設置 (使用默認值)`);
    } else {
      console.log(`   ✅ ${varName}: ${value}`);
    }
  });
  
  console.log('');
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 驗證 Firebase 項目配置
 */
function validateFirebaseProject(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  console.log('🔧 驗證 Firebase 項目配置...');
  
  const envInfo = getEnvironmentInfo();
  const expectedProject = envInfo.firebaseProject;
  const actualProject = app.options.projectId;
  
  console.log(`   期望項目: ${expectedProject}`);
  console.log(`   實際項目: ${actualProject}`);
  
  if (actualProject !== expectedProject) {
    errors.push(`Firebase 項目不匹配: 期望 ${expectedProject}, 實際 ${actualProject}`);
    console.log(`   ❌ 項目不匹配`);
  } else {
    console.log(`   ✅ 項目配置正確`);
  }
  
  // 檢查其他 Firebase 配置
  console.log(`   Auth Domain: ${app.options.authDomain}`);
  console.log(`   Database URL: ${app.options.databaseURL}`);
  console.log(`   Storage Bucket: ${app.options.storageBucket}`);
  
  console.log('');
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 驗證集合名稱配置
 */
function validateCollectionConfiguration(): { isValid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  console.log('📁 驗證集合名稱配置...');
  
  const envInfo = getEnvironmentInfo();
  const collections = envInfo.collections;
  
  // 檢查 Firestore 集合
  console.log('   Firestore 集合:');
  Object.entries(collections.firestore).forEach(([key, value]) => {
    if (!value || typeof value !== 'string') {
      errors.push(`Firestore 集合 ${key} 配置無效: ${value}`);
      console.log(`   ❌ ${key}: 無效配置`);
    } else {
      console.log(`   ✅ ${key}: ${value}`);
      
      // 檢查是否還有舊的 test_ 前綴
      if (value.startsWith('test_')) {
        warnings.push(`集合 ${key} 仍使用 test_ 前綴，在新的項目隔離模式下不需要`);
        console.log(`   ⚠️  ${key}: 使用了 test_ 前綴 (不推薦)`);
      }
    }
  });
  
  // 檢查 Realtime Database 路徑
  console.log('   Realtime Database 路徑:');
  Object.entries(collections.realtimeDB).forEach(([key, value]) => {
    if (!value || typeof value !== 'string') {
      errors.push(`Realtime Database 路徑 ${key} 配置無效: ${value}`);
      console.log(`   ❌ ${key}: 無效配置`);
    } else {
      console.log(`   ✅ ${key}: ${value}`);
      
      // 檢查是否還有舊的 test_ 前綴
      if (value.startsWith('test_')) {
        warnings.push(`路徑 ${key} 仍使用 test_ 前綴，在新的項目隔離模式下不需要`);
        console.log(`   ⚠️  ${key}: 使用了 test_ 前綴 (不推薦)`);
      }
    }
  });
  
  console.log('');
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 顯示環境摘要
 */
function displayEnvironmentSummary(): void {
  const envInfo = getEnvironmentInfo();
  
  console.log('📋 環境配置摘要');
  console.log('==========================================');
  console.log(`🌍 環境: ${envInfo.nodeEnv} (${envInfo.isDevelopment ? '開發' : '生產'})`);
  console.log(`📍 Firebase 項目: ${envInfo.firebaseProject}`);
  console.log(`🔧 隔離方法: ${envInfo.isolationMethod}`);
  console.log('');
  
  console.log('📊 集合配置來源: 環境變數');
  console.log('   Firestore 集合:');
  Object.entries(envInfo.collections.firestore).forEach(([key, value]) => {
    console.log(`     ${key}: ${value}`);
  });
  console.log('   Realtime Database 路徑:');
  Object.entries(envInfo.collections.realtimeDB).forEach(([key, value]) => {
    console.log(`     ${key}: ${value}`);
  });
  console.log('');
}

/**
 * 主驗證函數
 */
async function validateEnvironment(): Promise<void> {
  console.log('🔍 環境配置驗證');
  console.log('==========================================');
  
  let hasErrors = false;
  const allWarnings: string[] = [];
  
  try {
    // 1. 驗證環境變數
    const envVarResult = validateEnvironmentVariables();
    if (!envVarResult.isValid) {
      hasErrors = true;
      console.log('❌ 環境變數配置有錯誤:');
      envVarResult.errors.forEach(error => console.log(`   - ${error}`));
      console.log('');
    }
    allWarnings.push(...envVarResult.warnings);
    
    // 2. 驗證 Firebase 項目
    const firebaseResult = validateFirebaseProject();
    if (!firebaseResult.isValid) {
      hasErrors = true;
      console.log('❌ Firebase 項目配置有錯誤:');
      firebaseResult.errors.forEach(error => console.log(`   - ${error}`));
      console.log('');
    }
    
    // 3. 驗證集合配置
    const collectionResult = validateCollectionConfiguration();
    if (!collectionResult.isValid) {
      hasErrors = true;
      console.log('❌ 集合配置有錯誤:');
      collectionResult.errors.forEach(error => console.log(`   - ${error}`));
      console.log('');
    }
    allWarnings.push(...collectionResult.warnings);
    
    // 4. 顯示警告
    if (allWarnings.length > 0) {
      console.log('⚠️  配置警告:');
      allWarnings.forEach(warning => console.log(`   - ${warning}`));
      console.log('');
    }
    
    // 5. 顯示摘要
    displayEnvironmentSummary();
    
    // 6. 最終結果
    if (hasErrors) {
      console.log('❌ 環境配置驗證失敗！');
      console.log('請修復上述錯誤後重新運行驗證。');
      process.exit(1);
    } else {
      console.log('✅ 環境配置驗證通過！');
      if (allWarnings.length > 0) {
        console.log('⚠️  有一些警告，但不影響正常運行。');
      }
    }
    
  } catch (error) {
    console.log('❌ 驗證過程中發生錯誤:', error);
    process.exit(1);
  }
}

// 執行驗證
if (require.main === module) {
  validateEnvironment();
}

export { validateEnvironment };
