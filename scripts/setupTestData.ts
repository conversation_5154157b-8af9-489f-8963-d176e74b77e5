/**
 * 測試數據設置腳本
 * 用於在真實Firebase環境中創建測試集合並插入測試數據
 * 確保測試數據與生產數據完全分離
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator, collection, doc, setDoc, writeBatch, Timestamp } from 'firebase/firestore';
import { getDatabase, connectDatabaseEmulator, ref, set } from 'firebase/database';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFunctions, connectFunctionsEmulator, httpsCallable } from 'firebase/functions';
import * as dotenv from 'dotenv';

// 導入統一的集合管理工具
import { getCollectionConfig, getEnvironmentInfo, generateTestDeviceID, generateTestEventID, isTestData } from '../utils/firebaseCollections';

// 載入環境變數
dotenv.config();

// Firebase 配置 (使用真實項目配置)
const firebaseConfig = {
  apiKey: "AIzaSyBRO9smBJ8L7r7Wfr5vf9KHK2AoS9nZiQw",
  authDomain: "qmnoti.firebaseapp.com",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "qmnoti",
  storageBucket: "qmnoti.firebasestorage.app",
  messagingSenderId: "749977066729",
  appId: "1:749977066729:web:2b8c7f8a8b39c9e35cb19e"
};

// 初始化 Firebase
const app = initializeApp(firebaseConfig);
const firestore = getFirestore(app);
const database = getDatabase(app);
const auth = getAuth(app);
const functions = getFunctions(app, 'asia-east1');

/**
 * 測試用戶數據
 */
const testUsers = [
  {
    deviceID: 'test_user_001',
    nickname: '測試醫師A',
    fcmToken: 'test_fcm_token_001',
    name: '張醫師',
    role: 'doctor',
    initials: 'ZY',
    color: '#3B82F6',
    phoneNumber: '+852-1234-5678',
    avatar: ''
  },
  {
    deviceID: 'test_user_002',
    nickname: '測試護士B',
    fcmToken: 'test_fcm_token_002',
    name: '李護士',
    role: 'nurse',
    initials: 'LH',
    color: '#8B5CF6',
    phoneNumber: '+852-1234-5679',
    avatar: ''
  },
  {
    deviceID: 'test_user_003',
    nickname: '測試助理C',
    fcmToken: 'test_fcm_token_003',
    name: '王助理',
    role: 'assistant',
    initials: 'WZ',
    color: '#10B981',
    phoneNumber: '+852-1234-5680',
    avatar: ''
  },
  {
    deviceID: 'test_user_004',
    nickname: '測試管理員D',
    fcmToken: 'test_fcm_token_004',
    name: '陳管理員',
    role: 'manager',
    initials: 'CG',
    color: '#F59E0B',
    phoneNumber: '+852-1234-5681',
    avatar: ''
  }
];

/**
 * 測試群組數據
 */
const testGroups = [
  {
    id: 'test_group_001',
    name: '測試產科團隊',
    icon: 'local-hospital',
    color: '#EF4444',
    initials: 'CK',
    memberIds: ['test_user_001', 'test_user_002'],
    createdBy: 'test_user_001',
    isActive: true
  },
  {
    id: 'test_group_002',
    name: '測試新生兒科',
    icon: 'child-care',
    color: '#8B5CF6',
    initials: 'XSE',
    memberIds: ['test_user_002', 'test_user_003'],
    createdBy: 'test_user_002',
    isActive: true
  }
];

/**
 * 生成測試通知事件數據
 */
const generateTestEventData = () => {
  const events = [];
  const eventTypes = ['mother_baby_transfer', 'mother_only_transfer', 'baby_to_nicu'];
  
  for (let i = 1; i <= 5; i++) {
    const eventID = generateTestEventID();
    const caseType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
    const timestamp = Date.now() - (i * 3600000); // 每個事件間隔1小時
    
    events.push({
      eventID,
      initiatorDeviceID: 'test_user_001',
      initiatorNickname: '測試醫師A',
      caseType,
      motherInitial: `測試母親${String.fromCharCode(65 + i)}`,
      bedNumber: `${100 + i}`,
      designatedWard: `測試病房${i}`,
      clinicalNotes: `測試臨床記錄 ${i}`,
      createdAt: new Date(timestamp),
      status: Math.random() > 0.5 ? 'active' : 'resolved',
      totalRecipients: 2,
      acknowledgedCount: Math.floor(Math.random() * 3)
    });
  }
  
  return events;
};

/**
 * 清空測試數據
 */
async function clearTestData(): Promise<void> {
  console.log('🧹 開始清空測試數據...');
  
  const collections = getCollectionConfig();
  const batch = writeBatch(firestore);
  
  try {
    // 清空 Firestore 測試集合
    for (const testUser of testUsers) {
      const userRef = doc(firestore, collections.firestore.users, testUser.deviceID);
      batch.delete(userRef);
    }
    
    for (const testGroup of testGroups) {
      const groupRef = doc(firestore, collections.firestore.groups, testGroup.id);
      batch.delete(groupRef);
    }
    
    const testEvents = generateTestEventData();
    for (const event of testEvents) {
      const eventRef = doc(firestore, collections.firestore.alertEvents, event.eventID);
      batch.delete(eventRef);
    }
    
    await batch.commit();
    
    // 清空 Realtime Database 測試路徑
    await set(ref(database, collections.realtimeDB.presence), null);
    await set(ref(database, collections.realtimeDB.alertEvents), null);
    await set(ref(database, collections.realtimeDB.stats), null);
    
    console.log('✅ 測試數據清空完成');
  } catch (error) {
    console.error('❌ 清空測試數據失敗:', error);
    throw error;
  }
}

/**
 * 創建測試用戶數據
 */
async function createTestUsers(): Promise<void> {
  console.log('👥 開始創建測試用戶...');
  
  const collections = getCollectionConfig();
  const batch = writeBatch(firestore);
  const now = Timestamp.now();
  
  try {
    for (const user of testUsers) {
      const userData = {
        ...user,
        lastSeen: now,
        isActive: true,
        createdAt: now,
      };
      
      const userRef = doc(firestore, collections.firestore.users, user.deviceID);
      batch.set(userRef, userData);
      
      // 同時在 Realtime Database 創建在線狀態
      await set(ref(database, `${collections.realtimeDB.presence}/${user.deviceID}`), {
        isOnline: true,
        lastSeen: Date.now(),
        fcmTokenActive: true,
      });
    }
    
    await batch.commit();
    console.log(`✅ 成功創建 ${testUsers.length} 個測試用戶`);
  } catch (error) {
    console.error('❌ 創建測試用戶失敗:', error);
    throw error;
  }
}

/**
 * 創建測試群組數據
 */
async function createTestGroups(): Promise<void> {
  console.log('👥 開始創建測試群組...');
  
  const collections = getCollectionConfig();
  const batch = writeBatch(firestore);
  const now = Timestamp.now();
  
  try {
    for (const group of testGroups) {
      const groupData = {
        ...group,
        createdAt: now,
        updatedAt: now,
      };
      
      const groupRef = doc(firestore, collections.firestore.groups, group.id);
      batch.set(groupRef, groupData);
    }
    
    await batch.commit();
    console.log(`✅ 成功創建 ${testGroups.length} 個測試群組`);
  } catch (error) {
    console.error('❌ 創建測試群組失敗:', error);
    throw error;
  }
}

/**
 * 創建測試通知事件
 */
async function createTestAlertEvents(): Promise<void> {
  console.log('📢 開始創建測試通知事件...');
  
  const collections = getCollectionConfig();
  const events = generateTestEventData();
  const batch = writeBatch(firestore);
  
  try {
    for (const event of events) {
      // Firestore 事件記錄
      const eventRef = doc(firestore, collections.firestore.alertEvents, event.eventID);
      batch.set(eventRef, {
        ...event,
        createdAt: Timestamp.fromDate(event.createdAt)
      });
      
      // Realtime Database 實時狀態
      const realtimeEventData = {
        initiatorDeviceID: event.initiatorDeviceID,
        initiatorNickname: event.initiatorNickname,
        caseType: event.caseType,
        motherInitial: event.motherInitial,
        bedNumber: event.bedNumber,
        designatedWard: event.designatedWard,
        clinicalNotes: event.clinicalNotes,
        timestampCreated: event.createdAt.getTime(),
        status: event.status,
        recipients: {}
      };
      
      // 為每個接收者創建記錄，避免 undefined 值
      const recipientData1 = {
        nickname: '測試護士B',
        status: Math.random() > 0.5 ? 'acknowledged' : 'fcm_sent_pending_ack',
        fcmTokenUsed: 'test_fcm_token_002',
        lastUpdateTimestamp: Date.now()
      };
      
      const recipientData2 = {
        nickname: '測試助理C',
        status: Math.random() > 0.5 ? 'acknowledged' : 'fcm_sent_pending_ack',
        fcmTokenUsed: 'test_fcm_token_003',
        lastUpdateTimestamp: Date.now()
      };
      
      // 只有在狀態為 'acknowledged' 時才添加 acknowledgedTimestamp
      if (recipientData1.status === 'acknowledged') {
        (recipientData1 as any).acknowledgedTimestamp = Date.now();
      }
      
      if (recipientData2.status === 'acknowledged') {
        (recipientData2 as any).acknowledgedTimestamp = Date.now();
      }
      
      realtimeEventData.recipients = {
        'test_user_002': recipientData1,
        'test_user_003': recipientData2
      };
      
      await set(ref(database, `${collections.realtimeDB.alertEvents}/${event.eventID}`), realtimeEventData);
    }
    
    await batch.commit();
    console.log(`✅ 成功創建 ${events.length} 個測試通知事件`);
  } catch (error) {
    console.error('❌ 創建測試通知事件失敗:', error);
    throw error;
  }
}

/**
 * 創建測試統計數據
 */
async function createTestStats(): Promise<void> {
  console.log('📊 開始創建測試統計數據...');
  
  const collections = getCollectionConfig();
  
  try {
    const statsData = {
      totalAlerts: 15,
      activeAlerts: 3,
      todayAlerts: 5,
      lastUpdated: Date.now()
    };
    
    await set(ref(database, `${collections.realtimeDB.stats}/notifications`), statsData);
    console.log('✅ 成功創建測試統計數據');
  } catch (error) {
    console.error('❌ 創建測試統計數據失敗:', error);
    throw error;
  }
}

/**
 * 驗證測試數據
 */
async function verifyTestData(): Promise<void> {
  console.log('🔍 開始驗證測試數據...');
  
  const envInfo = getEnvironmentInfo();
  console.log('環境信息:', envInfo);
  
  // 檢查是否所有數據都有正確的測試前綴
  let validationPassed = true;
  
  // 檢查用戶數據
  for (const user of testUsers) {
    if (!isTestData(user)) {
      console.error(`❌ 用戶數據驗證失敗: ${user.deviceID} 不是有效的測試數據`);
      validationPassed = false;
    }
  }
  
  // 檢查群組數據
  for (const group of testGroups) {
    if (!isTestData(group)) {
      console.error(`❌ 群組數據驗證失敗: ${group.id} 不是有效的測試數據`);
      validationPassed = false;
    }
  }
  
  if (validationPassed) {
    console.log('✅ 測試數據驗證通過');
  } else {
    throw new Error('測試數據驗證失敗');
  }
}

/**
 * 主函數
 */
async function setupTestData(): Promise<void> {
  console.log('🚀 開始設置測試數據...');
  console.log('🌍 當前環境:', process.env.NODE_ENV);
  
  // 確保在開發環境中運行
  if (process.env.NODE_ENV !== 'development') {
    throw new Error('❌ 此腳本只能在開發環境 (NODE_ENV=development) 中運行');
  }
  
  try {
    // 1. 驗證測試數據格式
    await verifyTestData();
    
    // 2. 清空現有測試數據
    await clearTestData();
    
    // 3. 創建新的測試數據
    await createTestUsers();
    await createTestGroups();
    await createTestAlertEvents();
    await createTestStats();
    
    console.log('🎉 測試數據設置完成！');
    console.log('📋 摘要:');
    console.log(`   - 測試用戶: ${testUsers.length} 個`);
    console.log(`   - 測試群組: ${testGroups.length} 個`);
    console.log(`   - 測試事件: ${generateTestEventData().length} 個`);
    console.log(`   - 測試統計: 1 組`);
    
  } catch (error) {
    console.error('❌ 設置測試數據失敗:', error);
    process.exit(1);
  }
}

/**
 * 清理測試數據的獨立函數
 */
async function cleanupTestData(): Promise<void> {
  console.log('🧹 開始清理測試數據...');
  
  // 確保在開發環境中運行
  if (process.env.NODE_ENV !== 'development') {
    throw new Error('❌ 此腳本只能在開發環境 (NODE_ENV=development) 中運行');
  }
  
  try {
    await clearTestData();
    console.log('🎉 測試數據清理完成！');
  } catch (error) {
    console.error('❌ 清理測試數據失敗:', error);
    process.exit(1);
  }
}

// 根據命令行參數執行相應的操作
const command = process.argv[2];

switch (command) {
  case 'setup':
    setupTestData();
    break;
  case 'cleanup':
    cleanupTestData();
    break;
  default:
    console.log('用法:');
    console.log('  pnpm test-data setup   - 設置測試數據');
    console.log('  pnpm test-data cleanup - 清理測試數據');
    process.exit(1);
} 