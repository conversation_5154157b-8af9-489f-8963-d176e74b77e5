/**
 * Firebase 配置測試腳本
 * 驗證新的項目隔離配置是否正常工作
 */

import { app, auth, firestoreDB, realtimeDB, functions } from '../firebaseConfig';
import { getEnvironmentInfo } from '../utils/firebaseCollections';

/**
 * 測試 Firebase 配置
 */
async function testFirebaseConfig(): Promise<void> {
  console.log('🔍 測試 Firebase 配置...');
  console.log('==========================================');
  
  try {
    // 1. 測試環境信息
    console.log('📊 環境信息:');
    const envInfo = getEnvironmentInfo();
    console.log(`   NODE_ENV: ${envInfo.nodeEnv}`);
    console.log(`   開發環境: ${envInfo.isDevelopment}`);
    console.log(`   Firebase 項目: ${envInfo.firebaseProject}`);
    console.log(`   隔離方法: ${envInfo.isolationMethod}`);
    console.log('');
    
    // 2. 測試 Firebase App 配置
    console.log('🔧 Firebase App 配置:');
    console.log(`   項目 ID: ${app.options.projectId}`);
    console.log(`   Auth Domain: ${app.options.authDomain}`);
    console.log(`   Database URL: ${app.options.databaseURL}`);
    console.log('');
    
    // 3. 測試集合配置
    console.log('📁 集合配置 (來自環境變數):');
    console.log('   Firestore 集合:');
    Object.entries(envInfo.collections.firestore).forEach(([key, value]) => {
      const envVar = getEnvVarName(key, 'firestore');
      const envValue = process.env[envVar] || '使用默認值';
      console.log(`     ${key}: ${value} (${envVar}=${envValue})`);
    });
    console.log('   Realtime Database 路徑:');
    Object.entries(envInfo.collections.realtimeDB).forEach(([key, value]) => {
      const envVar = getEnvVarName(key, 'realtime');
      const envValue = process.env[envVar] || '使用默認值';
      console.log(`     ${key}: ${value} (${envVar}=${envValue})`);
    });
    console.log('');
    
    // 4. 驗證 Firebase 服務實例
    console.log('🔌 Firebase 服務實例:');
    console.log(`   Auth: ${auth ? '✅ 已初始化' : '❌ 未初始化'}`);
    console.log(`   Firestore: ${firestoreDB ? '✅ 已初始化' : '❌ 未初始化'}`);
    console.log(`   Realtime DB: ${realtimeDB ? '✅ 已初始化' : '❌ 未初始化'}`);
    console.log(`   Functions: ${functions ? '✅ 已初始化' : '❌ 未初始化'}`);
    console.log('');
    
    // 5. 驗證項目選擇邏輯
    console.log('🎯 項目選擇驗證:');
    const expectedProject = envInfo.isDevelopment ? 'qmnoti-test' : 'qmnoti';
    const actualProject = app.options.projectId;
    
    if (actualProject === expectedProject) {
      console.log(`   ✅ 項目選擇正確: ${actualProject}`);
    } else {
      console.log(`   ❌ 項目選擇錯誤: 期望 ${expectedProject}, 實際 ${actualProject}`);
    }
    
    // 6. 驗證集合名稱來自環境變數
    console.log('📋 集合名稱驗證:');
    const collections = envInfo.collections;
    
    // 檢查 Firestore 集合
    const firestoreCollections = Object.entries(collections.firestore);
    let collectionsValid = true;
    
    firestoreCollections.forEach(([key, value]) => {
      const envVar = getEnvVarName(key, 'firestore');
      const envValue = process.env[envVar];
      
      if (envValue && envValue === value) {
        console.log(`   ✅ ${key}: ${value} (來自 ${envVar})`);
      } else if (!envValue && isDefaultValue(key, value, 'firestore')) {
        console.log(`   ✅ ${key}: ${value} (使用默認值)`);
      } else {
        console.log(`   ❌ ${key}: 配置不匹配`);
        collectionsValid = false;
      }
    });
    
    // 檢查 Realtime Database 路徑
    const realtimeCollections = Object.entries(collections.realtimeDB);
    
    realtimeCollections.forEach(([key, value]) => {
      const envVar = getEnvVarName(key, 'realtime');
      const envValue = process.env[envVar];
      
      if (envValue && envValue === value) {
        console.log(`   ✅ ${key}: ${value} (來自 ${envVar})`);
      } else if (!envValue && isDefaultValue(key, value, 'realtime')) {
        console.log(`   ✅ ${key}: ${value} (使用默認值)`);
      } else {
        console.log(`   ❌ ${key}: 配置不匹配`);
        collectionsValid = false;
      }
    });
    
    console.log('');
    
    // 7. 總結
    console.log('📝 測試總結:');
    console.log('==========================================');
    
    if (actualProject === expectedProject && collectionsValid) {
      console.log('✅ 所有測試通過！Firebase 配置正確。');
      console.log(`   - 正確連接到 ${actualProject} 項目`);
      console.log('   - 集合名稱來自環境變數');
      console.log('   - 環境隔離通過項目分離實現');
    } else {
      console.log('❌ 測試失敗！請檢查配置。');
      if (actualProject !== expectedProject) {
        console.log(`   - 項目選擇錯誤: ${actualProject} != ${expectedProject}`);
      }
      if (!collectionsValid) {
        console.log('   - 集合配置錯誤');
      }
    }
    
  } catch (error) {
    console.error('❌ 測試過程中發生錯誤:', error);
    process.exit(1);
  }
}

/**
 * 獲取環境變數名稱
 */
function getEnvVarName(key: string, type: 'firestore' | 'realtime'): string {
  if (type === 'firestore') {
    const mapping: Record<string, string> = {
      users: 'FIRESTORE_USERS_COLLECTION',
      alertEvents: 'FIRESTORE_EVENTS_COLLECTION',
      groups: 'FIRESTORE_GROUPS_COLLECTION',
      staff: 'FIRESTORE_STAFF_COLLECTION'
    };
    return mapping[key] || `FIRESTORE_${key.toUpperCase()}_COLLECTION`;
  } else {
    const mapping: Record<string, string> = {
      presence: 'REALTIME_PRESENCE_PATH',
      alertEvents: 'REALTIME_EVENTS_PATH',
      stats: 'REALTIME_STATS_PATH'
    };
    return mapping[key] || `REALTIME_${key.toUpperCase()}_PATH`;
  }
}

/**
 * 檢查是否為默認值
 */
function isDefaultValue(key: string, value: string, type: 'firestore' | 'realtime'): boolean {
  if (type === 'firestore') {
    const defaults: Record<string, string> = {
      users: 'users',
      alertEvents: 'alertEvents',
      groups: 'groups',
      staff: 'staff'
    };
    return defaults[key] === value;
  } else {
    const defaults: Record<string, string> = {
      presence: 'presence',
      alertEvents: 'alertEvents',
      stats: 'stats'
    };
    return defaults[key] === value;
  }
}

/**
 * 主函數
 */
async function main(): Promise<void> {
  try {
    await testFirebaseConfig();
    console.log('\n🎉 Firebase 配置測試完成！');
  } catch (error) {
    console.error('❌ 測試失敗:', error);
    process.exit(1);
  }
}

// 執行測試
if (require.main === module) {
  main();
}

export { testFirebaseConfig };
