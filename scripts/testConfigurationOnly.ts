/**
 * 僅測試配置的腳本
 * 測試環境配置和Firebase連接，但不調用Cloud Functions
 */

import { app, auth, firestoreDB, realtimeDB, functions } from '../firebaseConfig';
import { getEnvironmentInfo } from '../utils/firebaseCollections';

/**
 * 測試環境配置
 */
async function testEnvironmentConfiguration(): Promise<void> {
  console.log('🔍 測試環境配置...');
  console.log('==========================================');
  
  // 1. 測試環境信息
  const envInfo = getEnvironmentInfo();
  console.log('📊 環境信息:');
  console.log(`   NODE_ENV: ${envInfo.nodeEnv}`);
  console.log(`   開發環境: ${envInfo.isDevelopment}`);
  console.log(`   Firebase 項目: ${envInfo.firebaseProject}`);
  console.log(`   隔離方法: ${envInfo.isolationMethod}`);
  console.log('');
  
  // 2. 測試 Firebase 配置
  console.log('🔧 Firebase 配置:');
  console.log(`   項目 ID: ${app.options.projectId}`);
  console.log(`   Auth Domain: ${app.options.authDomain}`);
  console.log(`   Database URL: ${app.options.databaseURL}`);
  console.log('');
  
  // 3. 測試集合配置
  console.log('📁 集合配置 (來自環境變數):');
  Object.entries(envInfo.collections.firestore).forEach(([key, value]) => {
    const envVar = getEnvVarName(key, 'firestore');
    const envValue = process.env[envVar] || '使用默認值';
    console.log(`   ${key}: ${value} (${envVar}=${envValue})`);
  });
  console.log('');
  
  // 4. 驗證 Firebase 服務實例
  console.log('🔌 Firebase 服務實例:');
  console.log(`   Auth: ${auth ? '✅ 已初始化' : '❌ 未初始化'}`);
  console.log(`   Firestore: ${firestoreDB ? '✅ 已初始化' : '❌ 未初始化'}`);
  console.log(`   Realtime DB: ${realtimeDB ? '✅ 已初始化' : '❌ 未初始化'}`);
  console.log(`   Functions: ${functions ? '✅ 已初始化' : '❌ 未初始化'}`);
  console.log('');
  
  // 5. 驗證項目選擇
  const expectedProject = envInfo.isDevelopment ? 'qmnoti-test' : 'qmnoti';
  const actualProject = app.options.projectId;
  
  if (actualProject === expectedProject) {
    console.log(`✅ 項目選擇正確: ${actualProject}`);
  } else {
    console.log(`❌ 項目選擇錯誤: 期望 ${expectedProject}, 實際 ${actualProject}`);
    throw new Error('Firebase 項目配置錯誤');
  }
  
  console.log('');
}

/**
 * 測試 Firestore 連接
 */
async function testFirestoreConnection(): Promise<void> {
  console.log('🗄️ 測試 Firestore 連接...');

  try {
    const envInfo = getEnvironmentInfo();
    const usersCollection = envInfo.collections.firestore.users;

    // 嘗試讀取一個不存在的文檔來測試連接
    const { doc, getDoc } = await import('firebase/firestore');
    const testDocRef = doc(firestoreDB, usersCollection, 'connection-test');

    console.log(`   測試集合: ${usersCollection}`);

    const docSnap = await getDoc(testDocRef);
    console.log(`   ✅ Firestore 連接成功 (文檔存在: ${docSnap.exists()})`);

  } catch (error: any) {
    if (error.code === 'permission-denied') {
      console.log(`   ✅ Firestore 連接成功 (權限拒絕是正常的，說明連接正常)`);
    } else {
      console.log(`   ❌ Firestore 連接失敗:`, error.message);
      throw error;
    }
  }
}

/**
 * 測試 Realtime Database 連接
 */
async function testRealtimeDBConnection(): Promise<void> {
  console.log('📊 測試 Realtime Database 連接...');
  
  try {
    const envInfo = getEnvironmentInfo();
    const presencePath = envInfo.collections.realtimeDB.presence;
    
    // 嘗試讀取一個路徑來測試連接
    const { ref, get } = await import('firebase/database');
    const testRef = ref(realtimeDB, `${presencePath}/connection-test`);
    
    console.log(`   測試路徑: ${presencePath}`);
    
    const snapshot = await get(testRef);
    console.log(`   ✅ Realtime Database 連接成功 (數據存在: ${snapshot.exists()})`);
    
  } catch (error) {
    console.log(`   ❌ Realtime Database 連接失敗:`, error);
    throw error;
  }
}

/**
 * 獲取環境變數名稱
 */
function getEnvVarName(key: string, type: 'firestore' | 'realtime'): string {
  if (type === 'firestore') {
    const mapping: Record<string, string> = {
      users: 'FIRESTORE_USERS_COLLECTION',
      alertEvents: 'FIRESTORE_EVENTS_COLLECTION',
      groups: 'FIRESTORE_GROUPS_COLLECTION',
      staff: 'FIRESTORE_STAFF_COLLECTION'
    };
    return mapping[key] || `FIRESTORE_${key.toUpperCase()}_COLLECTION`;
  } else {
    const mapping: Record<string, string> = {
      presence: 'REALTIME_PRESENCE_PATH',
      alertEvents: 'REALTIME_EVENTS_PATH',
      stats: 'REALTIME_STATS_PATH'
    };
    return mapping[key] || `REALTIME_${key.toUpperCase()}_PATH`;
  }
}

/**
 * 顯示部署建議
 */
function displayDeploymentAdvice(): void {
  const envInfo = getEnvironmentInfo();
  
  console.log('💡 Cloud Functions 部署建議');
  console.log('==========================================');
  
  if (envInfo.isDevelopment) {
    console.log('🧪 開發環境 (qmnoti-test 項目):');
    console.log('   ⚠️  測試項目需要升級到 Blaze 計劃才能部署 Cloud Functions');
    console.log('   🔗 升級連結: https://console.firebase.google.com/project/qmnoti-test/usage/details');
    console.log('   💰 Blaze 計劃按使用量付費，測試用途成本很低');
    console.log('');
    console.log('   部署命令 (升級後):');
    console.log('   1. firebase use test');
    console.log('   2. firebase deploy --only functions');
  } else {
    console.log('🚀 生產環境 (qmnoti 項目):');
    console.log('   ✅ 生產項目應該已經是 Blaze 計劃');
    console.log('');
    console.log('   部署命令:');
    console.log('   1. firebase use default');
    console.log('   2. firebase deploy --only functions');
  }
  
  console.log('');
  console.log('📋 部署前檢查清單:');
  console.log('   ✅ 環境配置正確');
  console.log('   ✅ Firebase 項目連接正確');
  console.log('   ✅ 集合名稱來自環境變數');
  console.log('   ✅ 數據庫連接正常');
  console.log('   ⏳ 等待 Cloud Functions 部署');
}

/**
 * 主測試函數
 */
async function runConfigurationTests(): Promise<void> {
  console.log('🚀 開始配置測試...');
  console.log('==========================================');
  
  try {
    // 1. 測試環境配置
    await testEnvironmentConfiguration();
    
    // 2. 測試 Firestore 連接
    await testFirestoreConnection();
    
    // 3. 測試 Realtime Database 連接
    await testRealtimeDBConnection();
    
    // 4. 顯示部署建議
    displayDeploymentAdvice();
    
    console.log('🎉 所有配置測試通過！');
    console.log('==========================================');
    console.log('✅ 環境配置正確');
    console.log('✅ Firebase 連接正常');
    console.log('✅ 數據庫訪問正常');
    console.log('✅ 準備好部署 Cloud Functions');
    
  } catch (error) {
    console.log('');
    console.log('❌ 配置測試失敗！');
    console.log('==========================================');
    console.error('錯誤詳情:', error);
    process.exit(1);
  }
}

// 執行測試
if (require.main === module) {
  runConfigurationTests();
}

export { runConfigurationTests };
