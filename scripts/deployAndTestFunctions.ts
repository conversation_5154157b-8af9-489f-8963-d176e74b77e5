/**
 * Cloud Functions 部署和測試腳本
 * 自動部署 Cloud Functions 並進行測試
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { getEnvironmentInfo } from '../utils/firebaseCollections';

const execAsync = promisify(exec);

/**
 * 執行命令並輸出結果
 */
async function runCommand(command: string, description: string): Promise<void> {
  console.log(`🔧 ${description}...`);
  console.log(`   命令: ${command}`);
  
  try {
    const { stdout, stderr } = await execAsync(command);
    
    if (stdout) {
      console.log('   輸出:', stdout.trim());
    }
    
    if (stderr) {
      console.log('   警告:', stderr.trim());
    }
    
    console.log(`   ✅ ${description}完成`);
  } catch (error: any) {
    console.log(`   ❌ ${description}失敗:`, error.message);
    throw error;
  }
  
  console.log('');
}

/**
 * 檢查 Firebase CLI 是否已安裝
 */
async function checkFirebaseCLI(): Promise<void> {
  console.log('🔍 檢查 Firebase CLI...');
  
  try {
    const { stdout } = await execAsync('firebase --version');
    console.log(`   ✅ Firebase CLI 版本: ${stdout.trim()}`);
  } catch (error) {
    console.log('   ❌ Firebase CLI 未安裝或不可用');
    console.log('   請安裝 Firebase CLI: npm install -g firebase-tools');
    throw error;
  }
  
  console.log('');
}

/**
 * 檢查當前 Firebase 項目
 */
async function checkCurrentProject(): Promise<string> {
  console.log('📍 檢查當前 Firebase 項目...');
  
  try {
    const { stdout } = await execAsync('firebase use');
    const lines = stdout.trim().split('\n');
    const currentLine = lines.find(line => line.includes('(current)'));
    
    if (currentLine) {
      const projectId = currentLine.split(' ')[0];
      console.log(`   ✅ 當前項目: ${projectId}`);
      return projectId;
    } else {
      throw new Error('無法確定當前 Firebase 項目');
    }
  } catch (error: any) {
    console.log('   ❌ 檢查項目失敗:', error.message);
    throw error;
  }
}

/**
 * 切換到正確的 Firebase 項目
 */
async function switchToCorrectProject(): Promise<string> {
  const envInfo = getEnvironmentInfo();
  const expectedProject = envInfo.firebaseProject;
  
  console.log(`🔄 切換到 ${expectedProject} 項目...`);
  
  try {
    await runCommand(`firebase use ${expectedProject}`, `切換到 ${expectedProject}`);
    return expectedProject;
  } catch (error) {
    console.log(`   ❌ 切換項目失敗，請確認項目 ${expectedProject} 存在`);
    throw error;
  }
}

/**
 * 構建 Cloud Functions
 */
async function buildFunctions(): Promise<void> {
  console.log('🔨 構建 Cloud Functions...');
  
  try {
    // 進入 functions 目錄並構建
    await runCommand('cd functions && pnpm run build', '構建 TypeScript');
  } catch (error) {
    console.log('   ❌ 構建失敗，請檢查 TypeScript 代碼');
    throw error;
  }
}

/**
 * 部署 Cloud Functions
 */
async function deployFunctions(): Promise<void> {
  console.log('🚀 部署 Cloud Functions...');
  
  try {
    await runCommand('firebase deploy --only functions', '部署 Cloud Functions');
  } catch (error) {
    console.log('   ❌ 部署失敗，請檢查配置和權限');
    throw error;
  }
}

/**
 * 等待部署完成
 */
async function waitForDeployment(): Promise<void> {
  console.log('⏳ 等待部署完成...');
  
  // 等待 30 秒讓部署完全生效
  await new Promise(resolve => setTimeout(resolve, 30000));
  
  console.log('   ✅ 等待完成');
  console.log('');
}

/**
 * 運行測試
 */
async function runFunctionTests(): Promise<void> {
  console.log('🧪 運行 Cloud Functions 測試...');
  
  try {
    await runCommand('npx tsx scripts/testEnvironmentAndFunctions.ts', '執行功能測試');
  } catch (error) {
    console.log('   ❌ 測試失敗');
    throw error;
  }
}

/**
 * 顯示部署摘要
 */
function displayDeploymentSummary(projectId: string): void {
  const envInfo = getEnvironmentInfo();
  
  console.log('📋 部署摘要');
  console.log('==========================================');
  console.log(`🎯 目標項目: ${projectId}`);
  console.log(`🌍 環境: ${envInfo.isDevelopment ? '開發' : '生產'}`);
  console.log(`📊 集合配置: 來自環境變數`);
  console.log(`🔧 Functions 區域: asia-east1`);
  console.log('');
  
  console.log('📝 已部署的 Functions:');
  console.log('   - registerUser (用戶註冊)');
  console.log('   - createAlert (創建通知)');
  console.log('   - acknowledgeAlert (確認通知)');
  console.log('   - cancelAlert (取消通知)');
  console.log('   - initializeStaffData (初始化員工數據)');
  console.log('   - healthCheck (健康檢查)');
  console.log('');
  
  console.log('🔗 Functions URL:');
  console.log(`   https://asia-east1-${projectId}.cloudfunctions.net/`);
  console.log('');
}

/**
 * 主函數
 */
async function main(): Promise<void> {
  console.log('🚀 Cloud Functions 部署和測試流程');
  console.log('==========================================');
  
  try {
    // 1. 檢查環境
    const envInfo = getEnvironmentInfo();
    console.log(`🌍 當前環境: ${envInfo.nodeEnv} (${envInfo.isDevelopment ? '開發' : '生產'})`);
    console.log(`📍 目標項目: ${envInfo.firebaseProject}`);
    console.log('');
    
    // 2. 檢查 Firebase CLI
    await checkFirebaseCLI();
    
    // 3. 檢查並切換項目
    const currentProject = await checkCurrentProject();
    
    if (currentProject !== envInfo.firebaseProject) {
      await switchToCorrectProject();
    } else {
      console.log(`   ✅ 已在正確的項目: ${currentProject}`);
      console.log('');
    }
    
    // 4. 構建 Functions
    await buildFunctions();
    
    // 5. 部署 Functions
    await deployFunctions();
    
    // 6. 等待部署完成
    await waitForDeployment();
    
    // 7. 運行測試
    await runFunctionTests();
    
    // 8. 顯示摘要
    displayDeploymentSummary(envInfo.firebaseProject);
    
    console.log('🎉 部署和測試完成！');
    console.log('==========================================');
    
  } catch (error) {
    console.log('');
    console.log('❌ 部署或測試失敗！');
    console.log('==========================================');
    console.error('錯誤詳情:', error);
    process.exit(1);
  }
}

// 執行主函數
if (require.main === module) {
  main();
}

export { main as deployAndTestFunctions };
