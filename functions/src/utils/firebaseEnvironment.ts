/**
 * React Native 專用的 Firebase 環境配置工具
 * 解決 React Native 環境變數讀取和環境隔離問題
 */

// 導入環境變數（使用 react-native-dotenv）
// 注意：@env 只在 React Native 環境中可用
let NODE_ENV: string | undefined;

try {
  // 動態導入 @env，避免在 Node.js 環境中出錯
  const envModule = require('@env');
  NODE_ENV = envModule.NODE_ENV;
} catch (error) {
  // 在 Node.js 環境中，@env 不可用，這是正常的
  NODE_ENV = undefined;
}

/**
 * 集合配置介面
 */
interface CollectionConfig {
  firestore: {
    users: string;
    alertEvents: string;
    groups: string;
    staff: string;
  };
  realtimeDB: {
    presence: string;
    alertEvents: string;
    stats: string;
  };
}

/**
 * 環境信息介面
 */
interface EnvironmentInfo {
  isDevelopment: boolean;
  nodeEnv: string | undefined;
  collections: CollectionConfig;
  testPrefix: string;
  platform: 'react-native' | 'node';
}

/**
 * React Native 環境變數讀取
 * 使用 react-native-dotenv 從 .env 文件讀取環境變數
 */
const getNodeEnv = (): string => {
  // 優先使用 react-native-dotenv 讀取的環境變數
  if (NODE_ENV) {
    console.log(`🔍 從 @env 讀取到 NODE_ENV: ${NODE_ENV}`);
    return NODE_ENV;
  }

  // 備用：檢查 process.env（在某些情況下可能可用）
  if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV) {
    console.log(`🔍 從 process.env 讀取到 NODE_ENV: ${process.env.NODE_ENV}`);
    return process.env.NODE_ENV;
  }

  // 備用檢測方法：使用 __DEV__ 全局變數
  if (typeof __DEV__ !== 'undefined') {
    const env = __DEV__ ? 'development' : 'production';
    console.log(`🔍 從 __DEV__ 推斷環境: ${env} (__DEV__ = ${__DEV__})`);
    return env;
  }

  // 最後備用：默認生產環境
  console.log('🔍 無法檢測環境變數，默認使用 production');
  return 'production';
};

/**
 * 判斷是否為開發環境
 */
const isDevelopment = getNodeEnv() === 'development';

/**
 * 獲取測試數據前綴
 * React Native 環境下使用固定前綴，避免依賴 .env 文件
 */
const getTestDataPrefix = (): string => {
  // 在 React Native 中，我們使用固定的測試前綴
  // 避免依賴 .env 文件的動態讀取
  return 'test_';
};

/**
 * 生產環境集合配置
 */
const productionCollections: CollectionConfig = {
  firestore: {
    users: 'users',
    alertEvents: 'alertEvents',
    groups: 'groups',
    staff: 'staff'
  },
  realtimeDB: {
    presence: 'presence',
    alertEvents: 'alertEvents',
    stats: 'stats'
  }
};

/**
 * 測試環境集合配置
 * React Native 環境下使用固定的測試集合名稱
 */
const testCollections: CollectionConfig = {
  firestore: {
    users: 'test_users',
    alertEvents: 'test_alertEvents',
    groups: 'test_groups',
    staff: 'test_staff'
  },
  realtimeDB: {
    presence: 'test_presence',
    alertEvents: 'test_alertEvents',
    stats: 'test_stats'
  }
};

/**
 * 獲取當前環境的集合配置
 */
export function getCollectionConfig(): CollectionConfig {
  const config = isDevelopment ? testCollections : productionCollections;
  
  // 在開發環境下輸出使用的集合名稱
  if (isDevelopment) {
    console.log('🧪 React Native 開發環境：使用測試集合');
    console.log('📊 Firestore 集合:');
    Object.entries(config.firestore).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    console.log('📈 Realtime Database 路徑:');
    Object.entries(config.realtimeDB).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
  } else {
    console.log('🚀 React Native 生產環境：使用正式集合');
  }
  
  return config;
}

/**
 * 獲取 Firestore 集合名稱
 */
export function getFirestoreCollections() {
  return getCollectionConfig().firestore;
}

/**
 * 獲取 Realtime Database 路徑
 */
export function getRealtimeDatabasePaths() {
  return getCollectionConfig().realtimeDB;
}

/**
 * 檢查是否為測試數據
 */
export function isTestData(data: any): boolean {
  const prefix = getTestDataPrefix();
  
  // 檢查常見的測試數據標識
  if (typeof data === 'string') {
    return data.startsWith(prefix);
  }
  
  if (typeof data === 'object' && data !== null) {
    // 檢查 deviceID 或其他標識
    const deviceID = data.deviceID || data.id;
    if (deviceID && typeof deviceID === 'string') {
      return deviceID.startsWith(prefix);
    }
    
    // 檢查 nickname 或 name
    const nickname = data.nickname || data.name;
    if (nickname && typeof nickname === 'string') {
      return nickname.includes('Test') || nickname.includes('測試');
    }
  }
  
  return false;
}

/**
 * 生成測試設備 ID
 */
export function generateTestDeviceID(): string {
  const prefix = getTestDataPrefix();
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}device_${timestamp}_${random}`;
}

/**
 * 生成測試事件 ID
 */
export function generateTestEventID(): string {
  const prefix = getTestDataPrefix();
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}event_${timestamp}_${random}`;
}

/**
 * 獲取環境信息
 */
export function getEnvironmentInfo(): EnvironmentInfo {
  const nodeEnv = getNodeEnv();
  
  return {
    isDevelopment,
    nodeEnv,
    collections: getCollectionConfig(),
    testPrefix: getTestDataPrefix(),
    platform: 'react-native'
  };
}

/**
 * 調試環境信息
 */
export function debugEnvironmentInfo(): void {
  const envInfo = getEnvironmentInfo();
  
  console.log('🔍 React Native 環境檢測信息:');
  console.log(`   平台: ${envInfo.platform}`);
  console.log(`   NODE_ENV: ${envInfo.nodeEnv}`);
  console.log(`   開發環境: ${envInfo.isDevelopment}`);
  console.log(`   測試前綴: ${envInfo.testPrefix}`);
  console.log(`   __DEV__: ${typeof __DEV__ !== 'undefined' ? __DEV__ : 'undefined'}`);
  
  if (envInfo.isDevelopment) {
    console.log('📊 使用的集合配置:');
    console.log('   Firestore:', envInfo.collections.firestore);
    console.log('   Realtime DB:', envInfo.collections.realtimeDB);
  }
}

/**
 * 驗證環境配置
 */
export function validateEnvironmentConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const envInfo = getEnvironmentInfo();
  
  // 檢查環境變數
  if (!envInfo.nodeEnv) {
    warnings.push('NODE_ENV 未設置，使用默認值');
  }
  
  // 檢查集合配置
  const collections = envInfo.collections;
  
  // 驗證 Firestore 集合名稱
  Object.entries(collections.firestore).forEach(([key, value]) => {
    if (!value || typeof value !== 'string') {
      errors.push(`Firestore 集合 ${key} 配置無效: ${value}`);
    }
    
    if (envInfo.isDevelopment && !value.startsWith(envInfo.testPrefix)) {
      warnings.push(`開發環境下 Firestore 集合 ${key} 未使用測試前綴: ${value}`);
    }
  });
  
  // 驗證 Realtime Database 路徑
  Object.entries(collections.realtimeDB).forEach(([key, value]) => {
    if (!value || typeof value !== 'string') {
      errors.push(`Realtime Database 路徑 ${key} 配置無效: ${value}`);
    }
    
    if (envInfo.isDevelopment && !value.startsWith(envInfo.testPrefix)) {
      warnings.push(`開發環境下 Realtime Database 路徑 ${key} 未使用測試前綴: ${value}`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 導出默認配置
 */
export default {
  getCollectionConfig,
  getFirestoreCollections,
  getRealtimeDatabasePaths,
  getEnvironmentInfo,
  debugEnvironmentInfo,
  validateEnvironmentConfig,
  isTestData,
  generateTestDeviceID,
  generateTestEventID,
  isDevelopment
};
