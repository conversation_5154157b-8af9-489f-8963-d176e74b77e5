/**
 * Firebase 集合名稱管理工具 (Cloud Functions)
 * 使用環境變數定義的集合名稱，環境隔離通過不同的 Firebase 項目實現
 */

// 載入環境變數
import dotenv from 'dotenv';
dotenv.config();

/**
 * 集合配置介面
 */
interface CollectionConfig {
  firestore: {
    users: string;
    alertEvents: string;
    groups: string;
    staff: string;
  };
  realtimeDB: {
    presence: string;
    alertEvents: string;
    stats: string;
  };
}

/**
 * 判斷是否為開發環境
 */
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * 從環境變數獲取集合配置 (Cloud Functions)
 * 所有環境都使用 .env 文件中定義的集合名稱
 * Cloud Functions 會自動連接到部署它們的 Firebase 項目
 * 開發環境: qmnoti-test 項目
 * 生產環境: qmnoti 項目
 */
const getCollectionsFromEnv = (): CollectionConfig => {
  return {
    firestore: {
      users: process.env.FIRESTORE_USERS_COLLECTION || 'users',
      alertEvents: process.env.FIRESTORE_EVENTS_COLLECTION || 'alertEvents',
      groups: process.env.FIRESTORE_GROUPS_COLLECTION || 'groups',
      staff: process.env.FIRESTORE_STAFF_COLLECTION || 'staff'
    },
    realtimeDB: {
      presence: process.env.REALTIME_PRESENCE_PATH || 'presence',
      alertEvents: process.env.REALTIME_EVENTS_PATH || 'alertEvents',
      stats: process.env.REALTIME_STATS_PATH || 'stats'
    }
  };
};

/**
 * 獲取當前環境的集合配置
 */
export function getCollectionConfig(): CollectionConfig {
  // 從環境變數獲取集合配置，環境隔離通過不同的 Firebase 項目實現
  const config = getCollectionsFromEnv();
  
  // 輸出當前環境信息
  if (isDevelopment) {
    console.log('🧪 Cloud Functions 開發環境：使用測試 Firebase 項目 (qmnoti-test)');
    console.log('📊 集合名稱 (來自環境變數):');
    Object.entries(config.firestore).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    console.log('📈 Realtime Database 路徑:');
    Object.entries(config.realtimeDB).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
  } else {
    console.log('🚀 Cloud Functions 生產環境：使用正式 Firebase 項目 (qmnoti)');
    console.log('📊 集合名稱 (來自環境變數):', Object.values(config.firestore).join(', '));
  }
  
  return config;
}

/**
 * 獲取 Firestore 集合名稱
 */
export function getFirestoreCollections() {
  return getCollectionConfig().firestore;
}

/**
 * 獲取 Realtime Database 路徑
 */
export function getRealtimeDatabasePaths() {
  return getCollectionConfig().realtimeDB;
}

/**
 * 檢查是否為測試數據
 * 注意：在新的項目隔離模式下，測試數據存儲在獨立的 Firebase 項目中
 * 此函數主要用於向後兼容和數據清理
 */
export function isTestData(data: any): boolean {
  // 檢查常見的測試數據標識
  if (typeof data === 'string') {
    return data.includes('test') || data.includes('Test') || data.includes('測試');
  }
  
  if (typeof data === 'object' && data !== null) {
    // 檢查 deviceID 或其他標識
    const deviceID = data.deviceID || data.id;
    if (deviceID && typeof deviceID === 'string') {
      return deviceID.includes('test') || deviceID.includes('Test');
    }
    
    // 檢查 nickname 或 name
    const nickname = data.nickname || data.name;
    if (nickname && typeof nickname === 'string') {
      return nickname.includes('Test') || nickname.includes('測試') || nickname.includes('test');
    }
  }
  
  return false;
}

/**
 * 生成測試設備 ID
 */
export function generateTestDeviceID(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `test_device_${timestamp}_${random}`;
}

/**
 * 生成測試事件 ID
 */
export function generateTestEventID(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `test_event_${timestamp}_${random}`;
}

/**
 * 環境信息
 */
export function getEnvironmentInfo() {
  return {
    isDevelopment,
    nodeEnv: process.env.NODE_ENV,
    collections: getCollectionConfig(),
    firebaseProject: isDevelopment ? 'qmnoti-test' : 'qmnoti',
    isolationMethod: 'separate-firebase-projects'
  };
}
