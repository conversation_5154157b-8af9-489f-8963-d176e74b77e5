"use strict";
/**
 * Firebase 集合名稱管理工具
 * 根據環境自動選擇測試集合或生產集合
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCollectionConfig = getCollectionConfig;
exports.getFirestoreCollections = getFirestoreCollections;
exports.getRealtimeDatabasePaths = getRealtimeDatabasePaths;
exports.getTestDataPrefix = getTestDataPrefix;
exports.isTestData = isTestData;
exports.generateTestDeviceID = generateTestDeviceID;
exports.generateTestEventID = generateTestEventID;
exports.getEnvironmentInfo = getEnvironmentInfo;
// 載入環境變數
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
/**
 * 判斷是否為開發環境
 */
const isDevelopment = process.env.NODE_ENV === 'development';
/**
 * 生產環境集合配置
 */
const productionCollections = {
    firestore: {
        users: 'users',
        alertEvents: 'alertEvents',
        groups: 'groups',
        staff: 'staff'
    },
    realtimeDB: {
        presence: 'presence',
        alertEvents: 'alertEvents',
        stats: 'stats'
    }
};
/**
 * 測試環境集合配置
 */
const testCollections = {
    firestore: {
        users: process.env.TEST_FIRESTORE_USERS_COLLECTION || 'test_users',
        alertEvents: process.env.TEST_FIRESTORE_EVENTS_COLLECTION || 'test_alertEvents',
        groups: process.env.TEST_FIRESTORE_GROUPS_COLLECTION || 'test_groups',
        staff: process.env.TEST_FIRESTORE_STAFF_COLLECTION || 'test_staff'
    },
    realtimeDB: {
        presence: process.env.TEST_REALTIME_PRESENCE_PATH || 'test_presence',
        alertEvents: process.env.TEST_REALTIME_EVENTS_PATH || 'test_alertEvents',
        stats: process.env.TEST_REALTIME_STATS_PATH || 'test_stats'
    }
};
/**
 * 獲取當前環境的集合配置
 */
function getCollectionConfig() {
    const config = isDevelopment ? testCollections : productionCollections;
    // 在開發環境下輸出使用的集合名稱
    if (isDevelopment) {
        console.log('🧪 開發環境：使用測試集合');
        console.log('📊 Firestore 集合:');
        Object.entries(config.firestore).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
        console.log('📈 Realtime Database 路徑:');
        Object.entries(config.realtimeDB).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
    }
    else {
        console.log('🚀 生產環境：使用正式集合');
    }
    return config;
}
/**
 * 獲取 Firestore 集合名稱
 */
function getFirestoreCollections() {
    return getCollectionConfig().firestore;
}
/**
 * 獲取 Realtime Database 路徑
 */
function getRealtimeDatabasePaths() {
    return getCollectionConfig().realtimeDB;
}
/**
 * 獲取測試數據前綴
 */
function getTestDataPrefix() {
    return process.env.TEST_DATA_PREFIX || 'test_';
}
/**
 * 檢查是否為測試數據
 */
function isTestData(data) {
    const prefix = getTestDataPrefix();
    // 檢查常見的測試數據標識
    if (typeof data === 'string') {
        return data.startsWith(prefix);
    }
    if (typeof data === 'object' && data !== null) {
        // 檢查 deviceID 或其他標識
        const deviceID = data.deviceID || data.id;
        if (deviceID && typeof deviceID === 'string') {
            return deviceID.startsWith(prefix);
        }
        // 檢查 nickname 或 name
        const nickname = data.nickname || data.name;
        if (nickname && typeof nickname === 'string') {
            return nickname.includes('Test') || nickname.includes('測試');
        }
    }
    return false;
}
/**
 * 生成測試設備 ID
 */
function generateTestDeviceID() {
    const prefix = getTestDataPrefix();
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}device_${timestamp}_${random}`;
}
/**
 * 生成測試事件 ID
 */
function generateTestEventID() {
    const prefix = getTestDataPrefix();
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}event_${timestamp}_${random}`;
}
/**
 * 環境信息
 */
function getEnvironmentInfo() {
    return {
        isDevelopment,
        nodeEnv: process.env.NODE_ENV,
        collections: getCollectionConfig(),
        testPrefix: getTestDataPrefix()
    };
}
//# sourceMappingURL=firebaseCollections.js.map