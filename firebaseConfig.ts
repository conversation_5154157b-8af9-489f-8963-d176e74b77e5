import { initializeApp, getApps, type FirebaseApp } from 'firebase/app';
// const { getAnalytics } = require("firebase/analytics");
import { getAuth, type Auth } from 'firebase/auth';
import { getDatabase, type Database } from 'firebase/database';
import { getFirestore, type Firestore } from 'firebase/firestore';
import { getFunctions } from 'firebase/functions';

// 環境變數配置
// React Native: 環境變數在打包時自動注入，無需手動載入
// Node.js環境: 需要手動載入 dotenv
if (typeof window === 'undefined') {
  // Node.js 環境
  try {
    require('dotenv').config();
  } catch (error) {
    // dotenv 不存在時忽略錯誤
    console.warn('dotenv not found, using system environment variables');
  }
}

const productionConfig = {
  apiKey: "AIzaSyDWmByCJlz3zwo5TE8Vd2Ed4EKmWUlg0Qg",
  authDomain: "qmnoti.firebaseapp.com",
  projectId: "qmnoti",
  storageBucket: "qmnoti.appspot.com",
  messagingSenderId: "590755555605",
  appId: "1:590755555605:web:660cb1745c42231729211c",
  measurementId: "G-8F79PF308H",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

const testConfig = {
  apiKey: "AIzaSyDfTg1CV5U_bGR50SuvihQxemS_jZN-bz4",
  authDomain: "qmnoti-test.firebaseapp.com",
  projectId: "qmnoti-test",
  storageBucket: "qmnoti-test.firebasestorage.app",
  messagingSenderId: "175834354502",
  appId: "1:175834354502:web:053bd4096fb906ff4fcb47",
  measurementId: "G-N8CJ8GQWYF",
  databaseURL: "https://qmnoti-test-default-rtdb.asia-southeast1.firebasedatabase.app/"
};


/**
 * 檢查是否為開發環境
 * 在開發環境下使用測試 Firebase 項目，生產環境使用正式 Firebase 項目
 */
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * 根據環境選擇 Firebase 配置
 * development: 使用測試 Firebase 項目 (qmnoti-test)
 * production: 使用正式 Firebase 項目 (qmnoti)
 */
const firebaseConfig = isDevelopment ? testConfig : productionConfig;

// 避免重複初始化 Firebase App
let app: FirebaseApp;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

// 初始化 Firebase 服務
const firestoreDB: Firestore = getFirestore(app);
const realtimeDB: Database = getDatabase(app);
const auth: Auth = getAuth(app);
// Functions 使用 asia-east1 區域
const functions = getFunctions(app, 'asia-east1');

// 環境信息輸出
if (isDevelopment) {
  console.log('🧪 開發環境：使用測試 Firebase 項目');
  console.log('📍 Firebase 項目:', firebaseConfig.projectId);
  console.log('📍 Functions 區域: asia-east1');
  console.log('💡 數據完全隔離在測試項目中');
} else {
  console.log('🚀 生產環境：使用正式 Firebase 項目');
  console.log('📍 Firebase 項目:', firebaseConfig.projectId);
  console.log('📍 Functions 區域: asia-east1');
}

// 如果你需要 Analytics
// const analytics = getAnalytics(app);

// Android專用：確保React Native Firebase也能工作
console.log('✅ Firebase 初始化完成');

export { app, auth, firestoreDB, realtimeDB, functions };
